import 'dart:io';
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../models/image_resolution.dart';
import '../models/clipart_model.dart';
import '../utils/image_utils.dart';
import '../widgets/clipart_selection_widget.dart';
import 'post_composition_screen.dart';

class ImageEditorScreen extends StatefulWidget {
  final File imageFile;

  const ImageEditorScreen({super.key, required this.imageFile});

  @override
  State<ImageEditorScreen> createState() => _ImageEditorScreenState();
}

class _ImageEditorScreenState extends State<ImageEditorScreen> {
  final ImageResolution _targetResolution = ImageResolution.fixedResolution;
  Size? _imageSize;
  bool _isLoading = false;
  bool _isUploading = false;

  // Image transformation controller for zoom and pan
  final TransformationController _transformationController =
      TransformationController();

  // Clip art overlay management
  final List<ClipArtOverlay> _clipArtOverlays = [];
  ClipArtOverlay? _selectedOverlay;
  int _overlayIdCounter = 0;

  @override
  void initState() {
    super.initState();
    _loadImageDimensions();
  }

  void _initializeImageTransform() {
    // Reset transformation to show the whole image initially
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _transformationController.value = Matrix4.identity();
      }
    });
  }

  Future<void> _loadImageDimensions() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final dimensions = await ImageUtils.getImageDimensions(widget.imageFile);
      setState(() {
        _imageSize = dimensions;
      });

      // Initialize the image transform after dimensions are loaded
      _initializeImageTransform();
    } catch (e) {
      print('Error loading image dimensions: $e');
      _showErrorSnackBar('Failed to load image');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _processAndContinue() async {
    if (_imageSize == null) return;

    try {
      setState(() {
        _isUploading = true;
      });

      // Calculate crop area based on the current transformation
      final cropRect = _calculateCropArea();

      // Process the image with black background and clip art overlays
      final processedFile = await ImageUtils.processImageWithClipArt(
        originalFile: widget.imageFile,
        targetResolution: _targetResolution,
        cropArea: cropRect,
        originalImageSize: _imageSize!,
        backgroundColor: Colors.black,
        clipArtOverlays: _clipArtOverlays,
      );

      if (mounted) {
        // Navigate to post composition screen
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) =>
                    PostCompositionScreen(croppedImageFile: processedFile),
          ),
        );
      }
    } catch (e) {
      print('Error processing image: $e');
      _showErrorSnackBar('Failed to process image');
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  Rect _calculateCropArea() {
    // Get the transformation matrix
    final matrix = _transformationController.value;

    // Extract translation and scale from the matrix
    final translation = matrix.getTranslation();
    final scale = matrix.getMaxScaleOnAxis();

    // Get the container dimensions (9:16 aspect ratio)
    final screenWidth =
        MediaQuery.of(context).size.width * 0.7; // 70% of screen width
    final containerHeight = screenWidth * (16 / 9); // 9:16 ratio (height/width)

    // Calculate the visible area in normalized coordinates (0.0 to 1.0)
    // The transformation matrix tells us how the image is positioned and scaled
    final normalizedX = (-translation.x / scale) / screenWidth;
    final normalizedY = (-translation.y / scale) / containerHeight;
    final normalizedWidth = screenWidth / (screenWidth * scale);
    final normalizedHeight = containerHeight / (containerHeight * scale);

    // Clamp values to ensure they're within bounds
    return Rect.fromLTWH(
      normalizedX.clamp(0.0, 1.0),
      normalizedY.clamp(0.0, 1.0),
      normalizedWidth.clamp(0.0, 1.0 - normalizedX.clamp(0.0, 1.0)),
      normalizedHeight.clamp(0.0, 1.0 - normalizedY.clamp(0.0, 1.0)),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Clip art management methods
  void _addClipArt(ClipArtItem clipArtItem) {
    final overlay = ClipArtOverlay(
      item: clipArtItem,
      x: 0.5, // Center horizontally
      y: 0.5, // Center vertically
      scale: 1.0,
      rotation: 0.0,
      id: 'overlay_${_overlayIdCounter++}',
    );

    setState(() {
      _clipArtOverlays.add(overlay);
      _selectedOverlay = overlay;
    });
  }

  void _updateOverlay(ClipArtOverlay overlay) {
    final index = _clipArtOverlays.indexWhere((o) => o.id == overlay.id);
    if (index != -1) {
      setState(() {
        _clipArtOverlays[index] = overlay;
      });
    }
  }

  void _deleteSelectedOverlay() {
    if (_selectedOverlay != null) {
      setState(() {
        _clipArtOverlays.removeWhere((o) => o.id == _selectedOverlay!.id);
        _selectedOverlay = null;
      });
    }
  }

  void _showClipArtSelection() {
    showClipArtSelection(context, _addClipArt);
  }

  Widget _buildClipArtOverlay(
    ClipArtOverlay overlay,
    double containerWidth,
    double containerHeight,
  ) {
    final isSelected = _selectedOverlay?.id == overlay.id;

    return Positioned(
      left: overlay.x * containerWidth - (50 * overlay.scale / 2),
      top: overlay.y * containerHeight - (50 * overlay.scale / 2),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedOverlay = overlay;
          });
        },
        onPanUpdate: (details) {
          final newX =
              (overlay.x * containerWidth + details.delta.dx) / containerWidth;
          final newY =
              (overlay.y * containerHeight + details.delta.dy) /
              containerHeight;

          final updatedOverlay = overlay.copyWith(
            x: newX.clamp(0.0, 1.0),
            y: newY.clamp(0.0, 1.0),
          );

          _updateOverlay(updatedOverlay);
          setState(() {
            _selectedOverlay = updatedOverlay;
          });
        },
        onScaleUpdate: (details) {
          final updatedOverlay = overlay.copyWith(
            scale: (overlay.scale * details.scale).clamp(0.5, 3.0),
            rotation: overlay.rotation + details.rotation,
          );

          _updateOverlay(updatedOverlay);
          setState(() {
            _selectedOverlay = updatedOverlay;
          });
        },
        onDoubleTap: () {
          _deleteSelectedOverlay();
        },
        child: Transform.rotate(
          angle: overlay.rotation,
          child: Transform.scale(
            scale: overlay.scale,
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                border:
                    isSelected
                        ? Border.all(color: AppColors.gfGreen, width: 2)
                        : null,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Image.asset(
                overlay.item.assetPath,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.red.withValues(alpha: 0.3),
                    child: const Icon(
                      Icons.error,
                      color: Colors.white,
                      size: 20,
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.darkBlue,
      appBar: AppBar(
        title: const Text(
          'Edit Image',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.darkBlue,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
        elevation: 0,
        actions: [
          if (!_isUploading) ...[
            IconButton(
              onPressed: _showClipArtSelection,
              icon: const Icon(Icons.auto_awesome, color: AppColors.gfGreen),
              tooltip: 'Add Clip Art',
            ),
            TextButton(
              onPressed: _imageSize != null ? _processAndContinue : null,
              child: const Text(
                'Next',
                style: TextStyle(
                  color: AppColors.gfGreen,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: AppColors.gfGreen),
              )
              : SafeArea(
                child: Column(
                  children: [
                    // Instructions
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          const Text(
                            'Crop to 9:16 Ratio',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.gfOffWhite,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Pinch to zoom, drag to move the image within the frame',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColors.gfGrayText,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    // Image preview with 16:9 crop area
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 24),
                        child: _buildImageCropPreview(),
                      ),
                    ),

                    // Clip art controls (when selected)
                    if (_selectedOverlay != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        decoration: const BoxDecoration(
                          color: AppColors.gfDarkBackground100,
                          border: Border(
                            top: BorderSide(
                              color: AppColors.gfGrayText,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildClipArtControlButton(
                              icon: Icons.delete,
                              label: 'Delete',
                              onTap: _deleteSelectedOverlay,
                              color: Colors.red,
                            ),
                            _buildClipArtControlButton(
                              icon: Icons.close,
                              label: 'Deselect',
                              onTap: () {
                                setState(() {
                                  _selectedOverlay = null;
                                });
                              },
                              color: AppColors.gfGrayText,
                            ),
                          ],
                        ),
                      ),

                    // Upload button and loading indicator
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          if (_isUploading)
                            const CircularProgressIndicator(
                              color: AppColors.gfGreen,
                            )
                          else
                            SizedBox(
                              width: double.infinity,
                              height: 50,
                              child: ElevatedButton(
                                onPressed:
                                    _imageSize != null
                                        ? _processAndContinue
                                        : null,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.gfGreen,
                                  foregroundColor: AppColors.darkBlue,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: const Text(
                                  'Continue',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildImageCropPreview() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate 9:16 aspect ratio container (portrait)
        final containerWidth =
            constraints.maxWidth * 0.7; // Use 70% of available width
        final containerHeight =
            containerWidth * (16 / 9); // 9:16 ratio (height/width)

        return Center(
          child: Container(
            width: containerWidth,
            height: containerHeight,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.gfGreen, width: 2),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Stack(
                children: [
                  // Main image with zoom/pan
                  InteractiveViewer(
                    transformationController: _transformationController,
                    minScale: 0.1,
                    maxScale: 4.0,
                    panEnabled: true,
                    scaleEnabled: true,
                    boundaryMargin: EdgeInsets.zero,
                    constrained: true,
                    child: SizedBox(
                      width: containerWidth,
                      height: containerHeight,
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: Image.file(widget.imageFile),
                      ),
                    ),
                  ),

                  // Clip art overlays
                  ..._clipArtOverlays.map(
                    (overlay) => _buildClipArtOverlay(
                      overlay,
                      containerWidth,
                      containerHeight,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildClipArtControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 18),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
