// lib/screens/clipart_editor_screen.dart

import 'dart:io';
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../models/clipart_model.dart';
import '../utils/image_utils.dart';
import '../widgets/clipart_selection_widget.dart';
import 'post_composition_screen.dart';

class ClipArtEditorScreen extends StatefulWidget {
  final File croppedImageFile;

  const ClipArtEditorScreen({super.key, required this.croppedImageFile});

  @override
  State<ClipArtEditorScreen> createState() => _ClipArtEditorScreenState();
}

class _ClipArtEditorScreenState extends State<ClipArtEditorScreen> {
  bool _isProcessing = false;

  // Clip art overlay management
  final List<ClipArtOverlay> _clipArtOverlays = [];
  ClipArtOverlay? _selectedOverlay;
  int _overlayIdCounter = 0;

  // Gesture tracking for better positioning
  ClipArtOverlay? _gestureStartOverlay;
  Offset? _gestureStartPosition;

  // Clip art management methods
  void _addClipArt(ClipArtItem clipArtItem) {
    final overlay = ClipArtOverlay(
      item: clipArtItem,
      x: 0.5, // Center horizontally
      y: 0.5, // Center vertically
      scale: 1.0,
      rotation: 0.0,
      id: 'overlay_${_overlayIdCounter++}',
    );

    setState(() {
      _clipArtOverlays.add(overlay);
      _selectedOverlay = overlay;
    });
  }

  void _updateOverlay(ClipArtOverlay overlay) {
    final index = _clipArtOverlays.indexWhere((o) => o.id == overlay.id);
    if (index != -1) {
      setState(() {
        _clipArtOverlays[index] = overlay;
      });
    }
  }

  void _deleteSelectedOverlay() {
    if (_selectedOverlay != null) {
      setState(() {
        _clipArtOverlays.removeWhere((o) => o.id == _selectedOverlay!.id);
        _selectedOverlay = null;
      });
    }
  }

  void _showClipArtSelection() {
    showClipArtSelection(context, _addClipArt);
  }

  Future<void> _processAndContinue() async {
    try {
      setState(() {
        _isProcessing = true;
      });

      // Show loading dialog if there are clip art overlays to process
      if (_clipArtOverlays.isNotEmpty && mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => const Center(
                child: Card(
                  color: AppColors.gfDarkBackground100,
                  child: Padding(
                    padding: EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(color: AppColors.gfGreen),
                        SizedBox(height: 16),
                        Text(
                          'Adding clip art...',
                          style: TextStyle(
                            color: AppColors.gfOffWhite,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
        );
      }

      // Add minimum loading time to ensure user sees the loading dialog
      if (_clipArtOverlays.isNotEmpty) {
        await Future.delayed(const Duration(milliseconds: 500));
      }

      File finalImageFile;

      if (_clipArtOverlays.isEmpty) {
        // No clip art, use the cropped image as-is
        finalImageFile = widget.croppedImageFile;
      } else {
        // Process image with clip art overlays
        finalImageFile = await ImageUtils.processImageWithClipArt(
          originalFile: widget.croppedImageFile,
          clipArtOverlays: _clipArtOverlays,
        );
      }

      if (mounted) {
        // Close loading dialog if it was shown
        if (_clipArtOverlays.isNotEmpty) {
          Navigator.of(context).pop();
        }

        // Navigate to post composition screen with slide transition
        Navigator.of(context).push(
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) =>
                    PostCompositionScreen(croppedImageFile: finalImageFile),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.easeInOut;

              var tween = Tween(
                begin: begin,
                end: end,
              ).chain(CurveTween(curve: curve));

              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 300),
          ),
        );
      }
    } catch (e) {
      if (mounted && _clipArtOverlays.isNotEmpty) {
        // Close loading dialog if it's open
        Navigator.of(context).pop();
      }
      print('Error processing image with clip art: $e');
      _showErrorSnackBar('Failed to process image');
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Widget _buildClipArtOverlay(
    ClipArtOverlay overlay,
    double containerWidth,
    double containerHeight,
  ) {
    final isSelected = _selectedOverlay?.id == overlay.id;

    // Use proportional size based on container dimensions (18% of smaller dimension)
    // This matches the calculation used in image processing
    final baseSize =
        (containerWidth < containerHeight ? containerWidth : containerHeight) *
        0.18;
    final displaySize = baseSize * overlay.scale;

    return Positioned(
      left: overlay.x * containerWidth - (displaySize / 2),
      top: overlay.y * containerHeight - (displaySize / 2),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedOverlay = overlay;
          });
        },
        onScaleStart: (details) {
          _gestureStartOverlay = overlay;
          _gestureStartPosition = details.focalPoint;
        },
        onScaleUpdate: (details) {
          if (_gestureStartOverlay == null || _gestureStartPosition == null) {
            return;
          }

          // Handle both pan (when scale = 1.0) and scale/rotation
          if (details.scale == 1.0) {
            // Pure pan gesture - use absolute positioning
            final deltaX = details.focalPoint.dx - _gestureStartPosition!.dx;
            final deltaY = details.focalPoint.dy - _gestureStartPosition!.dy;

            final newX =
                (_gestureStartOverlay!.x * containerWidth + deltaX) /
                containerWidth;
            final newY =
                (_gestureStartOverlay!.y * containerHeight + deltaY) /
                containerHeight;

            final updatedOverlay = overlay.copyWith(
              x: newX.clamp(0.0, 1.0),
              y: newY.clamp(0.0, 1.0),
            );

            _updateOverlay(updatedOverlay);
            setState(() {
              _selectedOverlay = updatedOverlay;
            });
          } else {
            // Scale and rotation gesture
            final updatedOverlay = overlay.copyWith(
              scale: (_gestureStartOverlay!.scale * details.scale).clamp(
                0.5,
                3.0,
              ),
              rotation: _gestureStartOverlay!.rotation + details.rotation,
            );

            _updateOverlay(updatedOverlay);
            setState(() {
              _selectedOverlay = updatedOverlay;
            });
          }
        },
        onScaleEnd: (details) {
          _gestureStartOverlay = null;
          _gestureStartPosition = null;
        },
        onDoubleTap: () {
          _deleteSelectedOverlay();
        },
        child: Transform.rotate(
          angle: overlay.rotation,
          child: Container(
            width: displaySize,
            height: displaySize,
            decoration: BoxDecoration(
              border:
                  isSelected
                      ? Border.all(color: AppColors.gfGreen, width: 2)
                      : null,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Image.asset(
              overlay.item.assetPath,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.red.withValues(alpha: 0.3),
                  child: const Icon(Icons.error, color: Colors.white, size: 20),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.darkBlue,
      appBar: AppBar(
        title: const Text(
          'Add Clip Art',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.darkBlue,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
        elevation: 0,
        actions: [
          if (!_isProcessing) ...[
            IconButton(
              onPressed: _showClipArtSelection,
              icon: const Icon(Icons.auto_awesome, color: AppColors.gfGreen),
              tooltip: 'Add Clip Art',
            ),
            TextButton(
              onPressed: _processAndContinue,
              child: const Text(
                'Next',
                style: TextStyle(
                  color: AppColors.gfGreen,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Instructions
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  const Text(
                    'Add Clip Art to Your Image',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.gfOffWhite,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Tap the ✨ button to add clip art, then drag, pinch, and rotate to position',
                    style: TextStyle(fontSize: 14, color: AppColors.gfGrayText),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // Image preview with clip art overlays
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 24),
                child: _buildImagePreview(),
              ),
            ),

            // Clip art controls (when selected)
            if (_selectedOverlay != null)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                decoration: const BoxDecoration(
                  color: AppColors.gfDarkBackground100,
                  border: Border(
                    top: BorderSide(color: AppColors.gfGrayText, width: 0.5),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildClipArtControlButton(
                      icon: Icons.delete,
                      label: 'Delete',
                      onTap: _deleteSelectedOverlay,
                      color: Colors.red,
                    ),
                    _buildClipArtControlButton(
                      icon: Icons.close,
                      label: 'Deselect',
                      onTap: () {
                        setState(() {
                          _selectedOverlay = null;
                        });
                      },
                      color: AppColors.gfGrayText,
                    ),
                  ],
                ),
              ),

            // Continue button
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  if (_isProcessing)
                    const CircularProgressIndicator(color: AppColors.gfGreen)
                  else
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _processAndContinue,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.gfGreen,
                          foregroundColor: AppColors.darkBlue,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          _clipArtOverlays.isEmpty
                              ? 'Skip Clip Art'
                              : 'Continue with Clip Art',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagePreview() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate container size to maintain aspect ratio - must match ImageEditorScreen exactly
        final containerWidth =
            constraints.maxWidth * 0.7; // 70% to match crop preview
        final containerHeight =
            containerWidth *
            (16 / 9); // 9:16 ratio (portrait: height = width * 16/9)

        return Center(
          child: Container(
            width: containerWidth,
            height: containerHeight,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.gfGreen, width: 2),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Stack(
                children: [
                  // Main image
                  Positioned.fill(
                    child: Image.file(
                      widget.croppedImageFile,
                      fit: BoxFit.cover,
                    ),
                  ),

                  // Clip art overlays
                  ..._clipArtOverlays.map(
                    (overlay) => _buildClipArtOverlay(
                      overlay,
                      containerWidth,
                      containerHeight,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildClipArtControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 18),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
